<template>
  <el-dialog
    :title="title"
    :visible.sync="visible"
    width="80%"
  >
    <div>
      <div ref="echarts" style="width: 100%;height: 400px"></div>
      <el-table
        ref="myTable"
        class="adapter-height"
        :data="dataList"
        border
        v-loading="dataListLoading"
        :header-cell-style="{ background: '#f8f9fa' }"
        style="width: 100%"
      >
        <el-table-column
          v-for="data in dataForm"
          :key="data.prop"
          :prop="data.prop"
          header-align="center"
          align="center"
          :label="data.label"
          min-width="90"
          :fixed="data.fixed"
        >
          <template slot-scope="scope">
            <div :style="data.style && data.style(scope.row[data.prop])">
              {{ data.formatter ? data.formatter(scope.row[data.prop]) : scope.row[data.prop] }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          fixed="right"
          header-align="center"
          align="center"
          width="100"
          label="操作"
          v-if="false"
        >
          <template slot-scope="scope">
            <el-button
              type="text"
              size="small"
              @click="addCallback(scope.row)"
            >
              回传
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="editOCPC(scope.row)"
            >
              出价
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

  </el-dialog>
</template>

<script>
import { mixinElTableAdapterHeight } from '@/mixins'
import Decimal from 'decimal.js'
import * as echarts from 'echarts'

export default {
  mixins: [mixinElTableAdapterHeight],
  data() {
    return {
      visible: false,
      title: '',
      dataList: [],
      dataListLoading: false,
      chart: null,
      initChartOption: {
        title: {
          text: '消耗与ROI分时趋势'
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function (params) {
            let result = params[0].name + '<br/>'
            params.forEach(param => {
              if (param.seriesName === '消耗') {
                result += param.marker + param.seriesName + ': ¥' + param.value + '<br/>'
              } else {
                result += param.marker + param.seriesName + ': ' + param.value + '<br/>'
              }
            })
            return result
          }
        },
        legend: {
          data: ['消耗', 'ROI'],
          top: 30
        },
        xAxis: {
          type: 'category',
          name: '时间',
          boundaryGap: true,
          axisLabel: {
            formatter: function (value) {
              return value.toString().padStart(2, '0') + ':00'
            }
          }
        },
        yAxis: [
          {
            type: 'value',
            name: '消耗(元)',
            position: 'left',
            axisLabel: {
              formatter: '¥{value}'
            },
            splitLine: {
              show: false
            }
          },
          {
            type: 'value',
            name: 'ROI',
            position: 'right',
            min: function (value) {
              return Math.max(value.min - 0.1, 0).toFixed(1)
            },
            max: function (value) {
              return Math.max(value.max + 0.1, 1).toFixed(1)
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '消耗',
            type: 'bar',
            yAxisIndex: 0,
            itemStyle: {
              color: '#5470c6'
            },
            barWidth: '60%'
          },
          {
            name: 'ROI',
            type: 'line',
            yAxisIndex: 1,
            smooth: true,
            itemStyle: {
              color: '#91cc75'
            },
            lineStyle: {
              width: 3
            }
          }
        ]
      },
      dataForm: [
        { prop: 'ftime', label: '日期', fixed: 'left', formatter: this.toHour },
        { prop: 'cost', label: '消耗' },
        { prop: 'costExposureNum', label: '曝光' },
        { prop: 'adClickNum', label: '点击' },
        { prop: 'costEcpm', label: 'ECPM' },
        { prop: 'cpc', label: 'CPC' },
        { prop: 'ctr', label: 'CTR', formatter: this.toPercentage },
        { prop: 'formCnt', label: '表单提交量' },
        { prop: 'formPrice', label: 'CPA' },
        { prop: 'conversionRate', label: 'CVR', formatter: this.toPercentage2 },
        { prop: 'income', label: '预估收益' },
        { prop: 'xincome', label: '开屏收入' },
        { prop: 'ecpm', label: 'ECPM' },
        { prop: 'zong', label: '总收入' },
        { prop: 'roi', label: 'ROI', fixed: 'right', style: this.roiStyle },
      ],
    }
  },
  methods: {
    init(row) {
      this.visible = true
      this.title = row.accountId
      this.$nextTick(() => {
        this.getDataList(row.accountId, row.dt)
      })
    },
    initChart() {
      if (this.chart) {
        this.chart.dispose()
      }
      this.chart = echarts.init(this.$refs.echarts, null, this.initChartOption);
      window.addEventListener('resize', this.chart.resize)
    },
    updateChart() {
      if (!this.chart || !this.dataList.length) return

      // 准备消耗数据
      const costData = this.dataList.map(item => ({
        name: item.ftime,
        value: item.cost || 0
      }))

      // 准备ROI数据
      const roiData = this.dataList.map(item => ({
        name: item.ftime,
        value: item.roi || 0
      }))

      // 获取时间轴数据
      const xAxisData = this.dataList.map(item => item.ftime)

      const option = {
        ...this.initChartOption,
        xAxis: {
          ...this.initChartOption.xAxis,
          data: xAxisData
        },
        series: [
          {
            ...this.initChartOption.series[0],
            data: costData.map(item => item.value)
          },
          {
            ...this.initChartOption.series[1],
            data: roiData.filter(item => item.value > 0 && item.value <= 5).map(item => item.value)
          }
        ]
      }

      this.chart.setOption(option, true)
    },
    getDataList(id, dt) {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/stat/quickrealtimehourreport/list'),
        method: 'get',
        params: this.$http.adornParams({
          page: 1,
          limit: 1000,
          accountId: id,
          dt: dt
        }),
      }).then(({ data }) => {
        if (data && data.code === 0) {
          this.dataList = data.page.list
            .map(it => {
              if (it.adClickNum > 0) {
                it.formCnt = (it.adClickNum * it.conversionRate).toFixed(0)
                it.formPrice = (it.cost / (it.adClickNum * it.conversionRate)).toFixed(2)
              }
              it.zong = this.addEarnings(it.income, it.xincome)
              return it
            })
            .sort((a, b) => a.ftime - b.ftime) // 按时间排序
        } else {
          this.dataList = []
        }
        this.dataListLoading = false

        // 数据加载完成后初始化并更新图表
        this.$nextTick(() => {
          this.initChart()
          this.updateChart()
        })
      })
    },
    addEarnings(a, b) {
      if (!a && !b) {
        return 0
      }
      if (!b) {
        return a.toFixed(2)
      }
      return new Decimal(a).plus(b).toNumber().toFixed(2)
    },
    toPercentage(value) {
      return `${Math.round(value * 100)}%`
    },

    toHour(value) {
      //5 to 05:00
      return `${value.toString().padStart(2, '0')}:00`
    },

    toPercentage2(value) {
      return `${(value * 100).toFixed(2)}%`
    },
    roiStyle(value) {
      return value >= 1 ? 'color: #f56c6c' : 'color: #67c23a'
    }
  },
  beforeDestroy() {
    if (this.chart) {
      window.removeEventListener('resize', this.chart.resize)
      this.chart.dispose()
      this.chart = null
    }
  }
}
</script>

<style scoped>
</style>