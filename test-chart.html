<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ECharts 测试 - 消耗与ROI分时趋势</title>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div id="chart" style="width: 100%; height: 400px;"></div>
    
    <script>
        // 模拟数据
        const mockData = {
            hourlyData: [
                { ftime: 0, cost: 100, roi: 0.8 },
                { ftime: 1, cost: 150, roi: 0.9 },
                { ftime: 2, cost: 200, roi: 1.1 },
                { ftime: 3, cost: 180, roi: 1.2 },
                { ftime: 4, cost: 220, roi: 1.0 },
                { ftime: 5, cost: 250, roi: 1.3 },
                { ftime: 6, cost: 300, roi: 1.4 },
                { ftime: 7, cost: 280, roi: 1.2 },
                { ftime: 8, cost: 320, roi: 1.5 },
                { ftime: 9, cost: 350, roi: 1.6 }
            ],
            totalROIData: [
                { timePoint: '2024-01-01 00:00:00', roi: 0.7 },
                { timePoint: '2024-01-01 01:00:00', roi: 0.8 },
                { timePoint: '2024-01-01 02:00:00', roi: 1.0 },
                { timePoint: '2024-01-01 03:00:00', roi: 1.1 },
                { timePoint: '2024-01-01 04:00:00', roi: 0.9 },
                { timePoint: '2024-01-01 05:00:00', roi: 1.2 },
                { timePoint: '2024-01-01 06:00:00', roi: 1.3 },
                { timePoint: '2024-01-01 07:00:00', roi: 1.1 },
                { timePoint: '2024-01-01 08:00:00', roi: 1.4 },
                { timePoint: '2024-01-01 09:00:00', roi: 1.5 }
            ]
        };

        // 初始化图表
        const chart = echarts.init(document.getElementById('chart'));
        
        // 准备数据
        const costData = mockData.hourlyData.map(item => item.cost);
        const hourlyROIData = mockData.hourlyData.map(item => item.roi);
        const totalROIData = mockData.hourlyData.map(item => {
            const matchedROI = mockData.totalROIData.find(roi => {
                const roiHour = new Date(roi.timePoint).getHours();
                return roiHour === item.ftime;
            });
            return matchedROI ? matchedROI.roi : null;
        });
        const xAxisData = mockData.hourlyData.map(item => item.ftime);

        // 图表配置
        const option = {
            title: {
                text: '消耗与ROI分时趋势'
            },
            tooltip: {
                show: true,
                trigger: 'axis',
                axisPointer: {
                    type: 'cross'
                },
                formatter: function (params) {
                    let result = params[0].name + ':00<br/>';
                    params.forEach(param => {
                        if (param.seriesName === '消耗') {
                            result += param.marker + param.seriesName + ': ¥' + param.value + '<br/>';
                        } else if (param.seriesName === '分时ROI' || param.seriesName === '总ROI') {
                            result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                        }
                    });
                    return result;
                }
            },
            legend: {
                data: ['消耗', '分时ROI', '总ROI'],
                top: 30
            },
            xAxis: {
                type: 'category',
                name: '时间',
                boundaryGap: true,
                data: xAxisData,
                axisLabel: {
                    formatter: function (value) {
                        return value.toString().padStart(2, '0') + ':00';
                    }
                }
            },
            yAxis: [
                {
                    type: 'value',
                    name: '消耗(元)',
                    position: 'left',
                    axisLabel: {
                        formatter: '¥{value}'
                    },
                    splitLine: {
                        show: false
                    }
                },
                {
                    type: 'value',
                    name: 'ROI',
                    position: 'right',
                    axisLabel: {
                        formatter: '{value}'
                    }
                }
            ],
            series: [
                {
                    name: '消耗',
                    type: 'bar',
                    yAxisIndex: 0,
                    data: costData,
                    itemStyle: {
                        color: '#5470c6'
                    },
                    barWidth: '60%'
                },
                {
                    name: '分时ROI',
                    type: 'line',
                    yAxisIndex: 1,
                    data: hourlyROIData,
                    smooth: true,
                    itemStyle: {
                        color: '#91cc75'
                    },
                    lineStyle: {
                        width: 3
                    }
                },
                {
                    name: '总ROI',
                    type: 'line',
                    yAxisIndex: 1,
                    data: totalROIData,
                    smooth: true,
                    itemStyle: {
                        color: '#fac858'
                    },
                    lineStyle: {
                        width: 3,
                        type: 'dashed'
                    }
                }
            ]
        };

        chart.setOption(option);
        
        // 响应式
        window.addEventListener('resize', function() {
            chart.resize();
        });
    </script>
</body>
</html>
